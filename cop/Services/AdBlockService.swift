//
//  AdBlockService.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import Foundation
import WebKit
import OSLog

/// 轻量级广告屏蔽服务 - 混合方案：WebKit原生内容屏蔽 + JavaScript注入
@MainActor
final class AdBlockService: ObservableObject {
    static let shared = AdBlockService()

    // MARK: - 发布属性
    @Published var isEnabled: Bool = true
    @Published var statistics = AdBlockStatistics()
    @Published var customRules: [String] = []
    @Published var whitelist: Set<String> = []
    @Published var easyListEnabled: Bool = true
    @Published var useNativeContentBlocking: Bool = true

    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "AdBlockService")
    private let userDefaults = UserDefaults.standard
    private let easyListService = EasyListService.shared
    private let ruleCompiler = FilterRuleCompiler()
    private let contentBlockingService = ContentBlockingService()
    
    // MARK: - 配置键
    private enum Keys {
        static let isEnabled = "adblock_enabled"
        static let customRules = "adblock_custom_rules"
        static let whitelist = "adblock_whitelist"
        static let statistics = "adblock_statistics"
        static let easyListEnabled = "adblock_easylist_enabled"
        static let useNativeContentBlocking = "adblock_use_native_content_blocking"
    }
    
    private init() {
        loadSettings()
        logger.info("🛡️ AdBlockService 初始化完成")
    }
    
    // MARK: - 公共接口
    
    /// 获取广告屏蔽用户脚本
    func createUserScript() -> WKUserScript {
        let scriptSource = generateAdBlockScript()
        return WKUserScript(
            source: scriptSource,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
    }

    /// 获取针对特定域名优化的用户脚本
    func createUserScript(for domain: String) -> WKUserScript {
        let scriptSource = generateOptimizedAdBlockScript(for: domain)
        return WKUserScript(
            source: scriptSource,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
    }

    /// 应用混合广告屏蔽到WebView配置
    func applyHybridAdBlocking(to configuration: WKWebViewConfiguration) async {
        guard isEnabled else {
            logger.info("🛡️ 广告屏蔽已禁用")
            return
        }

        // 1. 应用WebKit原生内容屏蔽（如果启用）
        if useNativeContentBlocking {
            await contentBlockingService.applyContentBlocking(to: configuration)
        }

        // 2. 添加JavaScript注入作为补充
        let adBlockScript = createUserScript()
        configuration.userContentController.addUserScript(adBlockScript)

        // 3. 添加消息处理器
        let adBlockHandler = AdBlockMessageHandler()
        let easyListHandler = EasyListMessageHandler()
        configuration.userContentController.add(adBlockHandler, name: "adBlockStats")
        configuration.userContentController.add(easyListHandler, name: "easyListStats")

        logger.info("🛡️ 混合广告屏蔽配置已应用 (原生: \(self.useNativeContentBlocking), JS注入: true)")
    }

    /// 移除广告屏蔽配置
    func removeAdBlocking(from configuration: WKWebViewConfiguration) async {
        // 移除WebKit原生内容屏蔽
        await contentBlockingService.removeAllContentBlocking(from: configuration)

        // 移除消息处理器
        configuration.userContentController.removeScriptMessageHandler(forName: "adBlockStats")
        configuration.userContentController.removeScriptMessageHandler(forName: "easyListStats")

        logger.info("🛡️ 广告屏蔽配置已移除")
    }
    
    /// 检查URL是否在白名单中
    func isWhitelisted(_ url: URL) -> Bool {
        guard let host = url.host else { return false }
        return whitelist.contains(host) || whitelist.contains(where: { host.contains($0) })
    }
    
    /// 添加到白名单
    func addToWhitelist(_ host: String) {
        whitelist.insert(host)
        saveSettings()
        logger.info("✅ 已添加到白名单: \(host)")
    }
    
    /// 从白名单移除
    func removeFromWhitelist(_ host: String) {
        whitelist.remove(host)
        saveSettings()
        logger.info("❌ 已从白名单移除: \(host)")
    }
    
    /// 添加自定义规则
    func addCustomRule(_ rule: String) {
        guard !rule.isEmpty && !customRules.contains(rule) else { return }
        customRules.append(rule)
        saveSettings()
        logger.info("📝 已添加自定义规则: \(rule)")
    }
    
    /// 移除自定义规则
    func removeCustomRule(_ rule: String) {
        customRules.removeAll { $0 == rule }
        saveSettings()
        logger.info("🗑️ 已移除自定义规则: \(rule)")
    }
    
    /// 更新统计信息
    func updateStatistics(blocked: Int = 0, allowed: Int = 0) {
        statistics.totalBlocked += blocked
        statistics.totalAllowed += allowed
        statistics.lastUpdated = Date()
        saveStatistics()
        logger.info("📊 广告屏蔽统计更新: 已屏蔽 \(self.statistics.totalBlocked), 已允许 \(self.statistics.totalAllowed)")
    }
    
    /// 处理来自JavaScript的统计消息
    func handleJavaScriptStats(blocked: Int) {
        updateStatistics(blocked: blocked)
    }

    /// 处理来自EasyList的统计消息
    func handleEasyListStats(blocked: Int) {
        updateStatistics(blocked: blocked)
    }

    /// 重置统计信息
    func resetStatistics() {
        statistics = AdBlockStatistics()
        saveStatistics()
        logger.info("📊 统计信息已重置")
    }

    /// 启用/禁用EasyList
    func toggleEasyList() {
        easyListEnabled.toggle()
        saveSettings()
        logger.info("🛡️ EasyList \(self.easyListEnabled ? "已启用" : "已禁用")")
    }
}

// MARK: - 广告屏蔽统计
struct AdBlockStatistics: Codable {
    var totalBlocked: Int = 0
    var totalAllowed: Int = 0
    var lastUpdated: Date = Date()
    
    var blockingRate: Double {
        let total = totalBlocked + totalAllowed
        return total > 0 ? Double(totalBlocked) / Double(total) : 0.0
    }
}

// MARK: - 私有方法
private extension AdBlockService {
    
    /// 生成广告屏蔽JavaScript脚本
    func generateAdBlockScript() -> String {
        guard isEnabled else {
            return "// AdBlock disabled"
        }

        let baseRules = getBaseAdBlockRules()
        let customRulesJS = customRules.map { "'\($0)'" }.joined(separator: ",")
        
        return """
        (function() {
            'use strict';
            
            // 广告屏蔽配置
            const adBlockConfig = {
                enabled: true,
                baseRules: [\(baseRules.map { "'\($0)'" }.joined(separator: ","))],
                customRules: [\(customRulesJS)],
                blockedCount: 0
            };
            
            // 优化的广告域名黑名单（更全面）
            const adDomains = [
                'doubleclick.net', 'googleadservices.com', 'googlesyndication.com',
                'googletagmanager.com', 'facebook.com/tr', 'amazon-adsystem.com',
                'adsystem.amazon.com', 'ads.yahoo.com', 'advertising.com',
                'adsystem.com', 'outbrain.com', 'taboola.com', 'criteo.com',
                'adsystem.com', 'scorecardresearch.com', 'quantserve.com',
                'adsystem.com', 'pubmatic.com', 'rubiconproject.com',
                'openx.net', 'adsystem.com', 'contextweb.com'
            ];
            
            // CSS选择器规则
            const adSelectors = [
                '[class*="ad-"]',
                '[class*="ads-"]',
                '[id*="ad-"]',
                '[id*="ads-"]',
                '.advertisement',
                '.ad-banner',
                '.ad-container',
                '.sponsored',
                '[data-ad]',
                'iframe[src*="ads"]'
            ].concat(adBlockConfig.baseRules).concat(adBlockConfig.customRules);
            
            // 优化的广告元素隐藏函数
            function hideAdElements() {
                try {
                    // 使用更高效的批量选择器查询
                    const combinedSelector = adSelectors.join(',');
                    const elements = document.querySelectorAll(combinedSelector);

                    // 批量处理元素
                    const elementsToHide = Array.from(elements).filter(element =>
                        element && element.style.display !== 'none' && !element.dataset.adBlocked
                    );

                    if (elementsToHide.length > 0) {
                        // 使用CSS类而不是内联样式，性能更好
                        if (!document.getElementById('cop-adblock-style')) {
                            const style = document.createElement('style');
                            style.id = 'cop-adblock-style';
                            style.textContent = '.cop-ad-blocked { display: none !important; visibility: hidden !important; opacity: 0 !important; height: 0 !important; width: 0 !important; }';
                            document.head.appendChild(style);
                        }

                        elementsToHide.forEach(element => {
                            element.classList.add('cop-ad-blocked');
                            element.dataset.adBlocked = 'true';
                            adBlockConfig.blockedCount++;
                        });
                    }
                } catch (error) {
                    console.log('AdBlock: 元素隐藏出错', error);
                }
            }
            
            // 拦截网络请求
            function interceptRequests() {
                // 拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && shouldBlockUrl(url)) {
                        adBlockConfig.blockedCount++;
                        return Promise.reject(new Error('Blocked by AdBlock'));
                    }
                    return originalFetch.apply(this, args);
                };
                
                // 拦截XMLHttpRequest
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, ...args) {
                    if (shouldBlockUrl(url)) {
                        adBlockConfig.blockedCount++;
                        throw new Error('Blocked by AdBlock');
                    }
                    return originalOpen.apply(this, [method, url, ...args]);
                };
            }
            
            // 优化的URL屏蔽检查函数
            function shouldBlockUrl(url) {
                if (!url || typeof url !== 'string') return false;

                // 缓存结果以提高性能
                if (adBlockConfig.urlCache && adBlockConfig.urlCache[url] !== undefined) {
                    return adBlockConfig.urlCache[url];
                }

                // 初始化缓存
                if (!adBlockConfig.urlCache) {
                    adBlockConfig.urlCache = {};
                }

                // 快速路径检查
                const lowerUrl = url.toLowerCase();
                let shouldBlock = false;

                // 检查域名黑名单
                for (const domain of adDomains) {
                    if (lowerUrl.includes(domain)) {
                        shouldBlock = true;
                        break;
                    }
                }

                // 检查路径模式
                if (!shouldBlock) {
                    const adPatterns = ['/ads/', '/advertisement/', '/sponsored/', '/banner/', '/popup/', '.ads.', '_ads_'];
                    shouldBlock = adPatterns.some(pattern => lowerUrl.includes(pattern));
                }

                // 缓存结果（限制缓存大小）
                if (Object.keys(adBlockConfig.urlCache).length < 1000) {
                    adBlockConfig.urlCache[url] = shouldBlock;
                }

                return shouldBlock;
            }
            
            // 优化的DOM观察器
            function observeDOM() {
                let debounceTimer = null;
                const observer = new MutationObserver(function(mutations) {
                    let hasRelevantChanges = false;

                    for (const mutation of mutations) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            // 检查是否有可能的广告元素
                            for (const node of mutation.addedNodes) {
                                if (node.nodeType === Node.ELEMENT_NODE) {
                                    const element = node;
                                    if (element.tagName === 'IFRAME' ||
                                        element.tagName === 'SCRIPT' ||
                                        element.className.toLowerCase().includes('ad') ||
                                        element.id.toLowerCase().includes('ad')) {
                                        hasRelevantChanges = true;
                                        break;
                                    }
                                }
                            }
                            if (hasRelevantChanges) break;
                        }
                    }

                    if (hasRelevantChanges) {
                        // 使用防抖动来减少频繁执行
                        clearTimeout(debounceTimer);
                        debounceTimer = setTimeout(hideAdElements, 200);
                    }
                });

                observer.observe(document.body || document.documentElement, {
                    childList: true,
                    subtree: true
                });
            }
            
            // 初始化广告屏蔽
            function initAdBlock() {
                hideAdElements();
                interceptRequests();
                
                if (document.body) {
                    observeDOM();
                } else {
                    document.addEventListener('DOMContentLoaded', observeDOM);
                }
                
                // 减少定期检查频率以提高性能
                setInterval(hideAdElements, 5000);
                
                console.log('🛡️ AdBlock 已激活');
            }
            
            // 启动
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initAdBlock);
            } else {
                initAdBlock();
            }
            
            // 向原生应用报告统计信息
            function reportStats() {
                if (window.webkit && window.webkit.messageHandlers && window.webkit.messageHandlers.adBlockStats) {
                    window.webkit.messageHandlers.adBlockStats.postMessage({
                        blocked: adBlockConfig.blockedCount,
                        timestamp: Date.now()
                    });
                    console.log('🛡️ AdBlock 统计报告: 已屏蔽', adBlockConfig.blockedCount);
                }
            }
            
            // 立即报告一次
            reportStats();
            
            // 每3秒报告一次
            setInterval(reportStats, 3000);
            
            // 在屏蔽广告时也立即报告
            const originalBlockedCount = adBlockConfig.blockedCount;
            const checkAndReport = () => {
                if (adBlockConfig.blockedCount > originalBlockedCount) {
                    reportStats();
                }
            };
            
            // 添加到所有屏蔽操作后
            setInterval(checkAndReport, 1000);
            
        })();
        """
    }

    /// 生成针对特定域名优化的广告屏蔽脚本
    func generateOptimizedAdBlockScript(for domain: String) -> String {
        guard isEnabled else {
            return "// AdBlock disabled"
        }

        // 基础脚本（自定义规则和基础规则）
        var combinedScript = generateAdBlockScript()

        // 如果启用了EasyList，添加优化的EasyList规则
        if easyListEnabled {
            let easyListRules = easyListService.getRules(for: domain)

            // 限制规则数量以提高性能（iPad mini A17 Pro优化）
            let maxRules = 5000
            let limitedRules = Array(easyListRules.prefix(maxRules))

            let networkRules = ruleCompiler.compileNetworkRules(limitedRules)
            let elementRules = ruleCompiler.compileElementRules(limitedRules)

            let easyListScript = ruleCompiler.generateOptimizedJavaScript(
                networkRules: networkRules,
                elementRules: elementRules,
                domain: domain
            )

            // 合并脚本
            combinedScript = """
            \(combinedScript)

            \(easyListScript)
            """
        }

        return combinedScript
    }
    
    /// 获取基础广告屏蔽规则
    func getBaseAdBlockRules() -> [String] {
        return [
            ".ad",
            ".ads",
            ".advert",
            ".advertisement",
            ".ad-banner",
            ".ad-container",
            ".ad-wrapper",
            ".sponsored-content",
            ".promotion",
            "[data-ad-slot]",
            "[data-google-ad]",
            "ins.adsbygoogle"
        ]
    }

    /// 加载设置
    func loadSettings() {
        // 默认启用广告屏蔽，除非用户明确禁用
        if userDefaults.object(forKey: Keys.isEnabled) == nil {
            isEnabled = true
            userDefaults.set(true, forKey: Keys.isEnabled)
        } else {
            isEnabled = userDefaults.bool(forKey: Keys.isEnabled)
        }

        // 默认启用EasyList，除非用户明确禁用
        if userDefaults.object(forKey: Keys.easyListEnabled) == nil {
            easyListEnabled = true
            userDefaults.set(true, forKey: Keys.easyListEnabled)
        } else {
            easyListEnabled = userDefaults.bool(forKey: Keys.easyListEnabled)
        }

        // 默认启用原生内容屏蔽，除非用户明确禁用
        if userDefaults.object(forKey: Keys.useNativeContentBlocking) == nil {
            useNativeContentBlocking = true
            userDefaults.set(true, forKey: Keys.useNativeContentBlocking)
        } else {
            useNativeContentBlocking = userDefaults.bool(forKey: Keys.useNativeContentBlocking)
        }

        customRules = userDefaults.stringArray(forKey: Keys.customRules) ?? []

        if let whitelistArray = userDefaults.stringArray(forKey: Keys.whitelist) {
            whitelist = Set(whitelistArray)
        }

        if let statisticsData = userDefaults.data(forKey: Keys.statistics),
           let loadedStats = try? JSONDecoder().decode(AdBlockStatistics.self, from: statisticsData) {
            statistics = loadedStats
            logger.info("📊 已加载广告屏蔽统计: 已屏蔽 \(self.statistics.totalBlocked), 已允许 \(self.statistics.totalAllowed)")
        } else {
            // 如果没有保存的统计数据，创建新的
            statistics = AdBlockStatistics()
            saveStatistics()
            logger.info("📊 创建新的广告屏蔽统计")
        }
    }

    /// 保存设置
    func saveSettings() {
        userDefaults.set(isEnabled, forKey: Keys.isEnabled)
        userDefaults.set(easyListEnabled, forKey: Keys.easyListEnabled)
        userDefaults.set(useNativeContentBlocking, forKey: Keys.useNativeContentBlocking)
        userDefaults.set(customRules, forKey: Keys.customRules)
        userDefaults.set(Array(whitelist), forKey: Keys.whitelist)
    }

    /// 保存统计信息
    func saveStatistics() {
        if let statisticsData = try? JSONEncoder().encode(statistics) {
            userDefaults.set(statisticsData, forKey: Keys.statistics)
        }
    }
}

// MARK: - 广告屏蔽消息处理器
class AdBlockMessageHandler: NSObject, WKScriptMessageHandler {
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        guard message.name == "adBlockStats",
              let messageBody = message.body as? [String: Any],
              let blocked = messageBody["blocked"] as? Int else {
            return
        }

        Task { @MainActor in
            AdBlockService.shared.handleJavaScriptStats(blocked: blocked)
        }
    }
}

// MARK: - EasyList消息处理器
class EasyListMessageHandler: NSObject, WKScriptMessageHandler {
    func userContentController(_ userContentController: WKUserContentController, didReceive message: WKScriptMessage) {
        guard message.name == "easyListStats",
              let messageBody = message.body as? [String: Any],
              let blocked = messageBody["blocked"] as? Int else {
            return
        }

        Task { @MainActor in
            AdBlockService.shared.handleEasyListStats(blocked: blocked)
        }
    }
}
