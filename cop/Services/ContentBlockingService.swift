//
//  ContentBlockingService.swift
//  cop
//
//  Created by Augment Agent on 2025/6/16.
//

import Foundation
import WebKit
import OSLog

/// WebKit原生内容屏蔽服务
@MainActor
final class ContentBlockingService: ObservableObject {
    
    // MARK: - 私有属性
    private let logger = Logger(subsystem: "com.cop.browser", category: "ContentBlockingService")
    private let ruleStore = WKContentRuleListStore.default()!
    private var compiledRuleLists: [String: WKContentRuleList] = [:]
    
    // MARK: - 常量
    private enum Constants {
        static let mainRuleListIdentifier = "cop_main_rules"
        static let easyListRuleListIdentifier = "cop_easylist_rules"
        static let customRuleListIdentifier = "cop_custom_rules"
        static let maxRulesPerList = 50000 // WebKit限制
    }
    
    // MARK: - 公共接口
    
    /// 编译并应用内容屏蔽规则到WebView配置
    func applyContentBlocking(to configuration: WKWebViewConfiguration) async {
        do {
            // 编译主要规则
            let mainRules = await compileMainRules()
            if let mainRuleList = try await compileRuleList(
                identifier: Constants.mainRuleListIdentifier,
                rules: mainRules
            ) {
                configuration.userContentController.add(mainRuleList)
                compiledRuleLists[Constants.mainRuleListIdentifier] = mainRuleList
                logger.info("✅ 主要内容屏蔽规则已应用: \(mainRules.count) 条")
            }
            
            // 编译EasyList规则（如果启用）
            if AdBlockService.shared.easyListEnabled {
                let easyListRules = await compileEasyListRules()
                if !easyListRules.isEmpty {
                    let chunkedRules = chunkRules(easyListRules, maxSize: Constants.maxRulesPerList)
                    
                    for (index, chunk) in chunkedRules.enumerated() {
                        let identifier = "\(Constants.easyListRuleListIdentifier)_\(index)"
                        if let ruleList = try await compileRuleList(
                            identifier: identifier,
                            rules: chunk
                        ) {
                            configuration.userContentController.add(ruleList)
                            compiledRuleLists[identifier] = ruleList
                        }
                    }
                    logger.info("✅ EasyList内容屏蔽规则已应用: \(easyListRules.count) 条，分为 \(chunkedRules.count) 个列表")
                }
            }
            
        } catch {
            logger.error("❌ 内容屏蔽规则编译失败: \(error.localizedDescription)")
        }
    }
    
    /// 移除所有内容屏蔽规则
    func removeAllContentBlocking(from configuration: WKWebViewConfiguration) {
        for ruleList in compiledRuleLists.values {
            configuration.userContentController.remove(ruleList)
        }
        compiledRuleLists.removeAll()
        logger.info("🗑️ 已移除所有内容屏蔽规则")
    }
    
    /// 重新编译规则
    func recompileRules() async {
        // 移除现有规则
        for identifier in compiledRuleLists.keys {
            await removeRuleList(identifier: identifier)
        }
        compiledRuleLists.removeAll()
        
        logger.info("🔄 内容屏蔽规则重新编译完成")
    }
}

// MARK: - 私有方法
private extension ContentBlockingService {
    
    /// 编译规则列表
    func compileRuleList(identifier: String, rules: [ContentBlockingRule]) async throws -> WKContentRuleList? {
        guard !rules.isEmpty else { return nil }
        
        // 转换为JSON
        let jsonRules = rules.map { $0.toJSON() }
        let jsonData = try JSONSerialization.data(withJSONObject: jsonRules, options: [])
        let jsonString = String(data: jsonData, encoding: .utf8) ?? "[]"
        
        // 检查是否已存在
        if let existingRuleList = try? await ruleStore.contentRuleList(forIdentifier: identifier) {
            return existingRuleList
        }
        
        // 编译新规则
        return try await ruleStore.compileContentRuleList(
            forIdentifier: identifier,
            encodedContentRuleList: jsonString
        )
    }
    
    /// 移除规则列表
    func removeRuleList(identifier: String) async {
        do {
            try await ruleStore.removeContentRuleList(forIdentifier: identifier)
        } catch {
            logger.warning("移除规则列表失败: \(identifier) - \(error.localizedDescription)")
        }
    }
    
    /// 编译主要屏蔽规则
    func compileMainRules() async -> [ContentBlockingRule] {
        var rules: [ContentBlockingRule] = []
        
        // 基础广告域名屏蔽
        let adDomains = [
            "doubleclick.net",
            "googleadservices.com",
            "googlesyndication.com",
            "googletagmanager.com",
            "facebook.com",
            "amazon-adsystem.com",
            "adsystem.amazon.com",
            "ads.yahoo.com",
            "advertising.com",
            "adsystem.com",
            "outbrain.com",
            "taboola.com",
            "criteo.com",
            "adsystem.com"
        ]
        
        for domain in adDomains {
            rules.append(ContentBlockingRule(
                trigger: ContentBlockingTrigger(urlFilter: ".*\(domain).*"),
                action: ContentBlockingAction(type: .block)
            ))
        }
        
        // 基础广告路径屏蔽
        let adPaths = ["/ads/", "/advertisement/", "/sponsored/", "/banner/", "/popup/"]
        for path in adPaths {
            rules.append(ContentBlockingRule(
                trigger: ContentBlockingTrigger(urlFilter: ".*\(path).*"),
                action: ContentBlockingAction(type: .block)
            ))
        }
        
        // CSS选择器隐藏规则
        let adSelectors = [
            ".ad", ".ads", ".advertisement", ".ad-banner", ".ad-container",
            ".sponsored", ".promotion", "[data-ad]", "[data-google-ad]",
            "ins.adsbygoogle", ".adsbygoogle"
        ]
        
        for selector in adSelectors {
            rules.append(ContentBlockingRule(
                trigger: ContentBlockingTrigger(urlFilter: ".*"),
                action: ContentBlockingAction(type: .cssDisplayNone, selector: selector)
            ))
        }
        
        return rules
    }
    
    /// 编译EasyList规则
    func compileEasyListRules() async -> [ContentBlockingRule] {
        let easyListRules = EasyListService.shared.getAllRules()
        var contentBlockingRules: [ContentBlockingRule] = []
        
        for rule in easyListRules.prefix(Constants.maxRulesPerList - 1000) { // 保留空间给其他规则
            if let contentRule = convertToContentBlockingRule(rule) {
                contentBlockingRules.append(contentRule)
            }
        }
        
        return contentBlockingRules
    }
    
    /// 将EasyList规则转换为WebKit内容屏蔽规则
    func convertToContentBlockingRule(_ rule: CompiledFilterRule) -> ContentBlockingRule? {
        switch rule.type {
        case .networkBlocking:
            let trigger = ContentBlockingTrigger(
                urlFilter: convertPatternToRegex(rule.pattern),
                resourceType: rule.options.types.isEmpty ? nil : Array(rule.options.types),
                loadType: rule.options.thirdParty == true ? ["third-party"] : nil,
                ifDomain: rule.options.domains.isEmpty ? nil : Array(rule.options.domains),
                unlessDomain: rule.options.excludedDomains.isEmpty ? nil : Array(rule.options.excludedDomains)
            )
            return ContentBlockingRule(
                trigger: trigger,
                action: ContentBlockingAction(type: .block)
            )
            
        case .elementHiding:
            let trigger = ContentBlockingTrigger(
                urlFilter: ".*",
                ifDomain: rule.options.domains.isEmpty ? nil : Array(rule.options.domains),
                unlessDomain: rule.options.excludedDomains.isEmpty ? nil : Array(rule.options.excludedDomains)
            )
            return ContentBlockingRule(
                trigger: trigger,
                action: ContentBlockingAction(type: .cssDisplayNone, selector: rule.pattern)
            )
            
        case .exception:
            let trigger = ContentBlockingTrigger(
                urlFilter: convertPatternToRegex(rule.pattern),
                ifDomain: rule.options.domains.isEmpty ? nil : Array(rule.options.domains)
            )
            return ContentBlockingRule(
                trigger: trigger,
                action: ContentBlockingAction(type: .ignorePreviousRules)
            )
            
        case .comment:
            return nil
        }
    }
    
    /// 转换EasyList模式为正则表达式
    func convertPatternToRegex(_ pattern: String) -> String {
        var regex = pattern
        
        // 处理EasyList特殊语法
        if regex.hasPrefix("||") {
            // ||example.com 转换为域名匹配
            let domain = String(regex.dropFirst(2))
            regex = "^https?://([^/]+\\.)?\\Q\(domain)\\E"
        } else if regex.hasPrefix("|") {
            // |http://example.com 转换为开始匹配
            regex = "^\\Q\(String(regex.dropFirst()))\\E"
        } else if regex.hasSuffix("|") {
            // example.com| 转换为结束匹配
            regex = "\\Q\(String(regex.dropLast()))\\E$"
        } else {
            // 普通模式，转义特殊字符
            regex = regex.replacingOccurrences(of: "*", with: ".*")
            regex = ".*\\Q\(regex)\\E.*"
        }
        
        return regex
    }
    
    /// 将规则分块以符合WebKit限制
    func chunkRules(_ rules: [ContentBlockingRule], maxSize: Int) -> [[ContentBlockingRule]] {
        var chunks: [[ContentBlockingRule]] = []
        var currentChunk: [ContentBlockingRule] = []
        
        for rule in rules {
            if currentChunk.count >= maxSize {
                chunks.append(currentChunk)
                currentChunk = []
            }
            currentChunk.append(rule)
        }
        
        if !currentChunk.isEmpty {
            chunks.append(currentChunk)
        }
        
        return chunks
    }
}
