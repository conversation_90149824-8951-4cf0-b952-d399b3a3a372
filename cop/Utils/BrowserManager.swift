//
//  BrowserManager.swift
//  cop
//
//  Created by Augment Agent on 2025/6/15.
//

import Foundation
import SwiftUI
import WebKit
import Network
import OSLog

// MARK: - 简化的浏览器性能指标
struct SimpleBrowserMetrics {
    var activeWebViews: Int = 0
    var memoryUsage: UInt64 = 0

    var description: String {
        return "WebViews: \(activeWebViews), Memory: \(ByteCountFormatter.string(fromByteCount: Int64(memoryUsage), countStyle: .memory))"
    }

    var healthStatus: String {
        if memoryUsage > 500 * 1024 * 1024 { // 500MB for iPad mini A17 Pro 8GB RAM - 更宽松的阈值
            return "内存使用过高"
        } else {
            return "运行正常"
        }
    }
}

/// 统一的浏览器管理器 - 整合WebView管理、网络监控和错误处理
@MainActor
final class BrowserManager: ObservableObject {
    static let shared = BrowserManager()
    
    // MARK: - 核心状态
    @Published private(set) var activeWebViews: [UUID: WKWebView] = [:]
    @Published private(set) var memoryUsage: UInt64 = 0
    @Published private(set) var currentMetrics = SimpleBrowserMetrics()
    // MARK: - 整合的性能监控（来自BrowserOptimizer）
    @Published private(set) var performanceGrade: PerformanceGrade = .good
    @Published private(set) var optimizationMetrics = OptimizationMetrics()
    private var interactionTimes: [TimeInterval] = []

    // MARK: - 服务依赖
    private let logger = Logger(subsystem: "com.cop.browser", category: "BrowserManager")

    // MARK: - 配置管理
    private let baseConfiguration: WKWebViewConfiguration
    
    // MARK: - 性能监控
    private var memoryCheckTimer: Timer?
    private var isMonitoring = false
    
    private init() {
        // 创建基础WebView配置
        let config = WKWebViewConfiguration()
        
        // 媒体配置 - 禁用自动播放
        config.mediaTypesRequiringUserActionForPlayback = [.video, .audio]

        // 安全配置
        config.preferences.javaScriptCanOpenWindowsAutomatically = false
        config.preferences.isFraudulentWebsiteWarningEnabled = true
        config.preferences.minimumFontSize = 9.0

        // 系统配置
        config.processPool = WKProcessPool()
        config.websiteDataStore = WKWebsiteDataStore.default()
        config.applicationNameForUserAgent = "cop Browser"

        // 权限配置
        if #available(iOS 14.0, *) {
            config.limitsNavigationsToAppBoundDomains = false
        }
        
        // 禁用不必要的功能以减少权限错误
        config.allowsInlineMediaPlayback = true
        config.allowsAirPlayForMediaPlayback = false
        config.allowsPictureInPictureMediaPlayback = false
        
        // 改进进程配置
        if #available(iOS 14.5, *) {
            config.upgradeKnownHostsToHTTPS = true
        }
        
        // 添加禁用自动播放的JavaScript
        let disableAutoplayScript = """
        (function() {
            function disableAutoplay() {
                const videos = document.querySelectorAll('video');
                videos.forEach(video => {
                    video.autoplay = false;
                    video.muted = false;
                    if (video.hasAttribute('autoplay')) {
                        video.removeAttribute('autoplay');
                    }
                });
            }
            
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', disableAutoplay);
            } else {
                disableAutoplay();
            }
            
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    mutation.addedNodes.forEach(function(node) {
                        if (node.nodeType === 1 && node.tagName === 'VIDEO') {
                            node.autoplay = false;
                            node.muted = false;
                            if (node.hasAttribute('autoplay')) {
                                node.removeAttribute('autoplay');
                            }
                        }
                    });
                });
            });
            
            observer.observe(document.body || document.documentElement, {
                childList: true,
                subtree: true
            });
        })();
        """
        
        let userScript = WKUserScript(
            source: disableAutoplayScript,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
        config.userContentController.addUserScript(userScript)

        self.baseConfiguration = config

        // 在所有属性初始化后设置广告屏蔽
        Task {
            await setupAdBlockConfiguration(config)
        }

        startMonitoring()
        logger.info("✅ BrowserManager 初始化完成")
    }

    deinit {
        memoryCheckTimer?.invalidate()
    }

    // MARK: - 公共接口

    /// 创建WebView - 统一入口点
    func createWebView(for tabId: UUID, userAgent: String) -> WKWebView {
        // 复制基础配置，避免共享冲突
        let configuration = baseConfiguration.copy() as! WKWebViewConfiguration

        // 创建WebView
        let webView = WKWebView(frame: .zero, configuration: configuration)

        // 配置WebView属性
        configureWebView(webView, userAgent: userAgent)

        // 注册管理
        activeWebViews[tabId] = webView
        updateMetrics()

        logger.info("🚀 WebView创建: \(tabId.uuidString.prefix(8))")
        return webView
    }

    /// 清理WebView - 统一处理
    func cleanupWebView(_ webView: WKWebView, for tabId: UUID) {
        webView.stopLoading()

        // 清理资源
        Task {
            await clearWebViewMemory(webView)
        }

        // 移除管理
        activeWebViews.removeValue(forKey: tabId)
        updateMetrics()

        logger.info("🧹 WebView清理: \(tabId.uuidString.prefix(8))")
    }

    /// 加载URL - 统一处理
    func loadURL(_ url: URL, in webView: WKWebView) {
        // 简单的HTTPS升级
        let secureURL = enforceHTTPS(for: url)

        // 创建优化请求
        let request = createNetworkRequest(for: secureURL)

        // 加载
        webView.load(request)

        logger.info("🌐 URL加载: \(secureURL.absoluteString.prefix(50))...")
    }

    /// 统一URL处理
    func processURLInput(_ input: String) -> URL? {
        let trimmedInput = input.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedInput.isEmpty else { return nil }

        // 1. 完整URL检查
        if let url = URL(string: trimmedInput), url.scheme != nil {
            return enforceHTTPS(for: url)
        }

        // 2. 域名格式检查
        if isValidDomain(trimmedInput) {
            if let url = URL(string: "https://\(trimmedInput)") {
                return enforceHTTPS(for: url)
            }
        }

        // 3. 搜索查询
        let query = trimmedInput.addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) ?? ""
        return URL(string: "https://www.google.com/search?q=\(query)")
    }

    /// 统一URL安全验证
    func validateURLSecurity(_ url: URL) -> Bool {
        // 简化版本：基本的URL验证
        return url.scheme == "https" || url.scheme == "http"
    }

    /// 简单的HTTPS强制升级
    private func enforceHTTPS(for url: URL) -> URL {
        if url.scheme == "http" {
            var components = URLComponents(url: url, resolvingAgainstBaseURL: false)
            components?.scheme = "https"
            return components?.url ?? url
        }
        return url
    }

    /// 内存优化（针对iPad mini A17 Pro 8GB RAM优化）
    func optimizeMemory() async {
        let usage = getCurrentMemoryUsage()
        memoryUsage = usage
        currentMetrics.memoryUsage = usage

        // 优化的内存压力检测 - 适应8GB设备
        let threshold: UInt64 = 500 * 1024 * 1024 // 从300MB增加到500MB，更适合8GB设备
        if usage > threshold {
            await performMemoryCleanup()
        }
    }



    // MARK: - 网络错误处理

    /// 简化的网络错误处理
    func handleNetworkError(_ error: Error, context: String = "", webView: WKWebView? = nil) {
        logger.error("🚨 网络错误: \(error.localizedDescription) - 上下文: \(context)")

        // 如果有WebView，显示错误页面
        if let webView = webView {
            Task { @MainActor in
                await self.handleWebViewError(error, webView: webView)
            }
        }
    }

    /// 创建简化的网络请求
    func createNetworkRequest(for url: URL) -> URLRequest {
        var request = URLRequest(url: url)

        // 基础配置
        request.timeoutInterval = 45.0 // 增加超时时间
        request.cachePolicy = .useProtocolCachePolicy

        // 添加基础请求头
        request.setValue("Mozilla/5.0 (iPad; CPU OS 18_4 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.0 Mobile/15E148 Safari/604.1 cop/1.0", forHTTPHeaderField: "User-Agent")
        request.setValue("gzip, deflate, br", forHTTPHeaderField: "Accept-Encoding")
        request.setValue("text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8", forHTTPHeaderField: "Accept")
        request.setValue("zh-CN,zh;q=0.9,en;q=0.8", forHTTPHeaderField: "Accept-Language")

        return request
    }

    /// 设置广告屏蔽配置
    private func setupAdBlockConfiguration(_ config: WKWebViewConfiguration) async {
        // 使用新的混合广告屏蔽方案
        await AdBlockService.shared.applyHybridAdBlocking(to: config)
        logger.info("🛡️ 广告屏蔽配置已应用")
    }

    /// 创建简化的广告屏蔽脚本
    private func createSimpleAdBlockScript() -> WKUserScript {
        let scriptSource = """
        (function() {
            'use strict';

            // 广告域名黑名单
            const adDomains = [
                'doubleclick.net',
                'googleadservices.com',
                'googlesyndication.com',
                'googletagmanager.com',
                'facebook.com/tr',
                'amazon-adsystem.com',
                'adsystem.amazon.com',
                'ads.yahoo.com',
                'advertising.com',
                'adsystem.com'
            ];

            // CSS选择器规则
            const adSelectors = [
                '[class*="ad-"]',
                '[class*="ads-"]',
                '[id*="ad-"]',
                '[id*="ads-"]',
                '.advertisement',
                '.ad-banner',
                '.ad-container',
                '.sponsored',
                '[data-ad]',
                'iframe[src*="ads"]',
                '.ad',
                '.ads',
                '.advert',
                'ins.adsbygoogle'
            ];

            // 隐藏广告元素
            function hideAdElements() {
                try {
                    adSelectors.forEach(selector => {
                        const elements = document.querySelectorAll(selector);
                        elements.forEach(element => {
                            if (element && element.style.display !== 'none') {
                                element.style.display = 'none';
                                element.style.visibility = 'hidden';
                                element.style.opacity = '0';
                                element.style.height = '0';
                                element.style.width = '0';
                            }
                        });
                    });
                } catch (error) {
                    console.log('AdBlock: 元素隐藏出错', error);
                }
            }

            // 拦截网络请求
            function interceptRequests() {
                // 拦截fetch请求
                const originalFetch = window.fetch;
                window.fetch = function(...args) {
                    const url = args[0];
                    if (typeof url === 'string' && shouldBlockUrl(url)) {
                        return Promise.reject(new Error('Blocked by AdBlock'));
                    }
                    return originalFetch.apply(this, args);
                };

                // 拦截XMLHttpRequest
                const originalOpen = XMLHttpRequest.prototype.open;
                XMLHttpRequest.prototype.open = function(method, url, ...args) {
                    if (shouldBlockUrl(url)) {
                        throw new Error('Blocked by AdBlock');
                    }
                    return originalOpen.apply(this, [method, url, ...args]);
                };
            }

            // 检查URL是否应该被屏蔽
            function shouldBlockUrl(url) {
                if (!url || typeof url !== 'string') return false;

                return adDomains.some(domain => url.includes(domain)) ||
                       url.includes('/ads/') ||
                       url.includes('advertisement') ||
                       url.includes('sponsored');
            }

            // 观察DOM变化
            function observeDOM() {
                const observer = new MutationObserver(function(mutations) {
                    let shouldCheck = false;
                    mutations.forEach(function(mutation) {
                        if (mutation.type === 'childList' && mutation.addedNodes.length > 0) {
                            shouldCheck = true;
                        }
                    });

                    if (shouldCheck) {
                        setTimeout(hideAdElements, 100);
                    }
                });

                observer.observe(document.body || document.documentElement, {
                    childList: true,
                    subtree: true
                });
            }

            // 初始化广告屏蔽
            function initAdBlock() {
                hideAdElements();
                interceptRequests();

                if (document.body) {
                    observeDOM();
                } else {
                    document.addEventListener('DOMContentLoaded', observeDOM);
                }

                // 定期检查新的广告元素
                setInterval(hideAdElements, 2000);

                console.log('🛡️ AdBlock 已激活');
            }

            // 启动
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', initAdBlock);
            } else {
                initAdBlock();
            }

        })();
        """

        return WKUserScript(
            source: scriptSource,
            injectionTime: .atDocumentStart,
            forMainFrameOnly: false
        )
    }

    // MARK: - 监控和性能管理

    /// 开始性能监控
    func startMonitoring() {
        guard !isMonitoring else { return }
        isMonitoring = true

        // 内存检查 - 每30秒
        memoryCheckTimer = Timer.scheduledTimer(withTimeInterval: 30.0, repeats: true) { [weak self] _ in
            Task { @MainActor in
                await self?.optimizeMemory()
            }
        }

        logger.info("🔄 开始性能监控")
    }

    /// 停止性能监控
    func stopMonitoring() {
        isMonitoring = false
        memoryCheckTimer?.invalidate()
        memoryCheckTimer = nil
        logger.info("⏹️ 停止性能监控")
    }

    // MARK: - 私有方法



    private func updateMetrics() {
        currentMetrics.activeWebViews = activeWebViews.count
        currentMetrics.memoryUsage = getCurrentMemoryUsage()
    }

    private func configureWebView(_ webView: WKWebView, userAgent: String) {
        // 用户代理
        webView.customUserAgent = userAgent

        // 基础属性
        webView.allowsLinkPreview = true
        webView.allowsBackForwardNavigationGestures = true
    }

    private func handleWebViewError(_ error: Error, webView: WKWebView) async {
        let nsError = error as NSError

        switch nsError.code {
        case NSURLErrorNotConnectedToInternet, NSURLErrorNetworkConnectionLost:
            await showOfflineErrorPage(webView: webView)
        case NSURLErrorTimedOut:
            await showTimeoutErrorPage(webView: webView)
        default:
            break
        }
    }

    private func showOfflineErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>网络连接错误</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">📡</div>
            <div class="error-title">网络连接失败</div>
            <div class="error-message">请检查您的网络连接后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }

    private func showTimeoutErrorPage(webView: WKWebView) async {
        let errorHTML = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>连接超时</title>
            <style>
                body { font-family: -apple-system, BlinkMacSystemFont, sans-serif; text-align: center; padding: 40px; }
                .error-icon { font-size: 64px; margin-bottom: 20px; }
                .error-title { font-size: 24px; font-weight: bold; margin-bottom: 10px; }
                .error-message { font-size: 16px; color: #666; margin-bottom: 30px; }
                .retry-button { background: #007AFF; color: white; border: none; padding: 12px 24px; border-radius: 8px; font-size: 16px; cursor: pointer; }
            </style>
        </head>
        <body>
            <div class="error-icon">⏱️</div>
            <div class="error-title">连接超时</div>
            <div class="error-message">服务器响应时间过长，请稍后重试</div>
            <button class="retry-button" onclick="window.location.reload()">重新加载</button>
        </body>
        </html>
        """
        webView.loadHTMLString(errorHTML, baseURL: nil)
    }

    private func isValidDomain(_ input: String) -> Bool {
        let domainRegex = "^([a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.)+(com|net|org|edu|gov|mil|biz|info|mobi|name|aero|jobs|museum|[a-z]{2})$"
        let domainPredicate = NSPredicate(format: "SELF MATCHES %@", domainRegex)
        return domainPredicate.evaluate(with: input.lowercased())
    }

    private func getCurrentMemoryUsage() -> UInt64 {
        var info = mach_task_basic_info()
        var count = mach_msg_type_number_t(MemoryLayout<mach_task_basic_info>.size)/4

        let kerr: kern_return_t = withUnsafeMutablePointer(to: &info) {
            $0.withMemoryRebound(to: integer_t.self, capacity: 1) {
                task_info(mach_task_self_, task_flavor_t(MACH_TASK_BASIC_INFO), $0, &count)
            }
        }

        if kerr == KERN_SUCCESS {
            return info.resident_size
        } else {
            return 0
        }
    }

    private func clearWebViewMemory(_ webView: WKWebView) async {
        // 清理WebView内存
        await webView.configuration.websiteDataStore.removeData(
            ofTypes: WKWebsiteDataStore.allWebsiteDataTypes(),
            modifiedSince: Date.distantPast
        )
    }

    private func performMemoryCleanup() async {
        logger.info("🧹 执行内存清理")

        // 清理不活跃的WebView
        let inactiveWebViews = activeWebViews.filter { _, webView in
            webView.superview == nil
        }

        for (tabId, webView) in inactiveWebViews {
            await clearWebViewMemory(webView)
            activeWebViews.removeValue(forKey: tabId)
        }

        // 强制垃圾回收
        autoreleasepool {
            // 触发内存回收
        }

        updateMetrics()
    }

    // MARK: - 整合的性能监控方法（来自BrowserOptimizer）

    /// 记录用户交互时间
    func recordInteractionTime(_ time: TimeInterval) {
        interactionTimes.append(time)

        // 保持历史记录在合理范围内
        if interactionTimes.count > 20 {
            interactionTimes.removeFirst()
        }

        // 更新性能等级
        updatePerformanceGrade()

        // 更新优化指标
        optimizationMetrics.updateAverageTime(interactionTimes)
    }

    /// 获取推荐的动画持续时间
    func getRecommendedAnimationDuration() -> TimeInterval {
        switch performanceGrade {
        case .excellent:
            return 0.3
        case .good:
            return 0.25
        case .fair:
            return 0.2
        case .poor:
            return 0.15
        }
    }

    /// 获取性能建议
    func getPerformanceRecommendations() -> [String] {
        var recommendations: [String] = []

        switch performanceGrade {
        case .excellent:
            recommendations.append("✅ 性能优秀，继续保持")
        case .good:
            recommendations.append("👍 性能良好")
        case .fair:
            recommendations.append("⚠️ 建议减少同时打开的标签页")
        case .poor:
            recommendations.append("🔴 建议关闭部分标签页以提升性能")
            recommendations.append("🔴 考虑重启应用以释放内存")
        }

        return recommendations
    }

    private func updatePerformanceGrade() {
        guard !interactionTimes.isEmpty else { return }

        let averageTime = interactionTimes.reduce(0, +) / Double(interactionTimes.count)

        switch averageTime {
        case 0...0.05:
            performanceGrade = .excellent
        case 0.05...0.1:
            performanceGrade = .good
        case 0.1...0.2:
            performanceGrade = .fair
        default:
            performanceGrade = .poor
        }
    }
}

// MARK: - 性能监控相关类型（整合自BrowserOptimizer）

enum PerformanceGrade: String, CaseIterable {
    case excellent = "优秀"
    case good = "良好"
    case fair = "一般"
    case poor = "需要改进"

    var color: Color {
        switch self {
        case .excellent: return .green
        case .good: return .blue
        case .fair: return .orange
        case .poor: return .red
        }
    }
}

struct OptimizationMetrics {
    var averageInteractionTime: TimeInterval = 0.0
    var optimizationCount: Int = 0
    var lastOptimizationDate: Date = Date()

    mutating func updateAverageTime(_ times: [TimeInterval]) {
        guard !times.isEmpty else { return }
        averageInteractionTime = times.reduce(0, +) / Double(times.count)
        optimizationCount += 1
        lastOptimizationDate = Date()
    }
}


